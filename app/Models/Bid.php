<?php

namespace App\Models;

use App\Models\User;
use App\Models\JobBooking;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Bid extends Model
{
    use HasFactory;

    protected $table = 'bids';

    protected $fillable = [
        'id',
        'job_booking_id',
        'provider_id',
        'amount',
        'description',
        'status',
        'estimated_completion_time'
    ];

    protected $with = [
        'provider:id,name,email'
    ];

    protected $casts = [
        'job_booking_id' => 'integer',
        'provider_id' => 'integer',
        'amount' => 'float',
        'estimated_completion_time' => 'datetime',
    ];

    /**
     * Get the job booking that owns the bid.
     */
    public function jobBooking(): BelongsTo
    {
        return $this->belongsTo(JobBooking::class, 'job_booking_id');
    }

    /**
     * Get the driver that owns the bid.
     */
    public function provider(): BelongsTo
    {
        return $this->belongsTo(User::class, 'provider_id');
    }
}
