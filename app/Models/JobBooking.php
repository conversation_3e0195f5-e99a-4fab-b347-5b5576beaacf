<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Str;
use App\Services\ProjectCodeGenerator;
use App\Enums\JobBookingStatusEnum;

class JobBooking extends Model
{
    use HasFactory;

    protected $table = 'job_bookings';

    protected $fillable = [
        'job_uuid',
        'project_code',
        'job_type',
        'property_type',
        'service_category',
        'service_tasks',
        'description',
        'schedule_date',
        'time_preference',
        'frequency',
        'recurring_frequency',
        'address',
        'city',
        'state',
        'zip_code',
        'contact_name',
        'contact_email',
        'contact_phone',
        'status',
        'user_id'
    ];

    protected $casts = [
        'schedule_date' => 'date',
        'service_tasks' => 'array',
    ];

    /**
     * Get the service tasks
     * 
     * @return array
     */
    public function getServiceTasksAttribute($value)
    {
        if (is_string($value)) {
            return json_decode($value, true) ?: [];
        }
        return is_array($value) ? $value : [];
    }

    public static function boot()
    {
        parent::boot();
        
        static::creating(function ($job) {
            $job->job_uuid = (string) Str::uuid();
            
            // Generate project code
            $projectCodeGenerator = new ProjectCodeGenerator();
            $job->project_code = $projectCodeGenerator->generateProjectCode($job->job_uuid);
        });
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function assets()
    {
        return $this->belongsToMany(Asset::class, 'job_booking_assets')
                    ->withTimestamps();
    }

    /**
     * Get the bids for this job booking
     */
    public function bids(): HasMany
    {
        return $this->hasMany(Bid::class, 'job_booking_id');
    }

    /**
     * Get the bookings for this job booking
     */
    public function bookings(): HasMany
    {
        return $this->hasMany(Booking::class, 'job_booking_id');
    }

    /**
     * Get the accepted bid for this job booking
     */
    public function acceptedBid()
    {
        return $this->bids()->where('status', 'accepted')->first();
    }

    /**
     * Get pending bids for this job booking
     */
    public function pendingBids(): HasMany
    {
        return $this->bids()->where('status', 'requested');
    }

    /**
     * Check if job booking allows bidding
     */
    public function allowsBidding(): bool
    {
        return JobBookingStatusEnum::allowsBidding($this->status);
    }

    /**
     * Check if job booking is in final status
     */
    public function isFinal(): bool
    {
        return JobBookingStatusEnum::isFinal($this->status);
    }
} 